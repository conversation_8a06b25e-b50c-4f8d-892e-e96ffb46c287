<view class="login-container">
  <view class="login-header">
    <view class="login-logo">
      <t-image src="https://tdesign.gtimg.com/miniprogram/template/retail/logo.png" width="120rpx" height="120rpx" />
    </view>
    <view class="login-title">欢迎登录</view>
    <view class="login-subtitle">请使用微信账号登录</view>
  </view>

  <view class="login-content">
    <!-- 微信登录按钮 -->
    <view class="login-btn-wrapper">
      <t-button
        block
        shape="round"
        theme="primary"
        size="large"
        loading="{{loginLoading}}"
        bind:tap="handleWechatLogin"
      >
        {{loginLoading ? '登录中...' : '微信一键登录'}}
      </t-button>
    </view>

    <!-- 备用：头像昵称填写组件登录 -->
    <view class="profile-login-section" wx:if="{{showProfileLogin}}">
      <view class="divider">
        <view class="divider-line"></view>
        <view class="divider-text">或</view>
        <view class="divider-line"></view>
      </view>

      <view class="profile-form">
        <view class="form-item">
          <text class="form-label">头像</text>
          <button class="avatar-btn" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
            <image class="avatar-img" src="{{userAvatar || defaultAvatar}}" />
          </button>
        </view>

        <view class="form-item">
          <text class="form-label">昵称</text>
          <input
            class="nickname-input"
            type="nickname"
            placeholder="请输入昵称"
            value="{{userNickname}}"
            bind:input="onNicknameInput"
          />
        </view>

        <view class="profile-login-btn-wrapper">
          <t-button
            block
            shape="round"
            theme="primary"
            size="large"
            disabled="{{!canProfileLogin}}"
            loading="{{profileLoginLoading}}"
            bind:tap="handleProfileLogin"
          >
            {{profileLoginLoading ? '登录中...' : '确认登录'}}
          </t-button>
        </view>
      </view>
    </view>

    <!-- 手机号登录 -->
    <view class="phone-login-section" wx:if="{{showPhoneLogin}}">
      <view class="divider">
        <view class="divider-line"></view>
        <view class="divider-text">或</view>
        <view class="divider-line"></view>
      </view>
      
      <view class="phone-input-wrapper">
        <t-input
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          value="{{phoneNumber}}"
          bind:change="onPhoneChange"
          t-class="phone-input"
        />
      </view>
      
      <view class="verification-wrapper">
        <t-input
          placeholder="请输入验证码"
          type="number"
          maxlength="6"
          value="{{verificationCode}}"
          bind:change="onCodeChange"
          t-class="code-input"
        />
        <t-button
          size="small"
          theme="primary"
          variant="outline"
          disabled="{{!canSendCode || codeCountdown > 0}}"
          bind:tap="sendVerificationCode"
          t-class="send-code-btn"
        >
          {{codeCountdown > 0 ? codeCountdown + 's后重发' : '发送验证码'}}
        </t-button>
      </view>
      
      <view class="phone-login-btn-wrapper">
        <t-button 
          block 
          shape="round" 
          theme="primary" 
          size="large"
          disabled="{{!canPhoneLogin}}"
          loading="{{phoneLoginLoading}}"
          bind:tap="handlePhoneLogin"
        >
          {{phoneLoginLoading ? '登录中...' : '手机号登录'}}
        </t-button>
      </view>
    </view>

    <!-- 切换登录方式 -->
    <view class="switch-login-type" bind:tap="toggleLoginType">
      {{showPhoneLogin ? '使用头像昵称登录' : showProfileLogin ? '仅使用微信登录' : '更多登录方式'}}
    </view>
  </view>

  <view class="login-footer">
    <view class="agreement-text">
      登录即表示同意
      <text class="agreement-link" bind:tap="showUserAgreement">《用户协议》</text>
      和
      <text class="agreement-link" bind:tap="showPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>
</view>

<!-- 用户协议弹窗 -->
<t-popup visible="{{showAgreement}}" placement="center" bind:visible-change="onAgreementClose">
  <view class="agreement-popup">
    <view class="agreement-title">用户协议</view>
    <scroll-view class="agreement-content" scroll-y>
      <text>这里是用户协议的内容...</text>
    </scroll-view>
    <view class="agreement-actions">
      <t-button size="small" theme="primary" bind:tap="closeAgreement">我知道了</t-button>
    </view>
  </view>
</t-popup>

<!-- 隐私政策弹窗 -->
<t-popup visible="{{showPrivacy}}" placement="center" bind:visible-change="onPrivacyClose">
  <view class="agreement-popup">
    <view class="agreement-title">隐私政策</view>
    <scroll-view class="agreement-content" scroll-y>
      <text>这里是隐私政策的内容...</text>
    </scroll-view>
    <view class="agreement-actions">
      <t-button size="small" theme="primary" bind:tap="closePrivacy">我知道了</t-button>
    </view>
  </view>
</t-popup>

<t-toast id="t-toast" />
