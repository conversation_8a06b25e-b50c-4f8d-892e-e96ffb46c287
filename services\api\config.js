import { config } from '../../config/index';

/**
 * API配置管理
 */
class ApiConfig {
  constructor() {
    this.baseURL = this.getBaseURL();
  }

  /**
   * 获取API基础地址
   */
  getBaseURL() {
    // 根据环境返回不同的API地址
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    
    switch (envVersion) {
      case 'develop':
        return config.devApiUrl || config.apiUrl;
      case 'trial':
        return config.apiUrl;
      case 'release':
        return config.prodApiUrl || config.apiUrl;
      default:
        return config.apiUrl;
    }
  }

  /**
   * 获取完整的API地址
   * @param {string} path - API路径
   * @returns {string} 完整的API地址
   */
  getApiUrl(path) {
    return `${this.baseURL}${path}`;
  }

  /**
   * 获取通用请求头
   * @param {Object} customHeaders - 自定义请求头
   * @returns {Object} 请求头对象
   */
  getHeaders(customHeaders = {}) {
    const token = wx.getStorageSync('token');
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    return { ...defaultHeaders, ...customHeaders };
  }

  /**
   * 通用请求方法
   * @param {Object} options - 请求配置
   * @returns {Promise} 请求结果
   */
  request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      headers = {},
      timeout = 10000
    } = options;

    return new Promise((resolve, reject) => {
      wx.request({
        url: this.getApiUrl(url),
        method,
        data: {
          ...data,
          platform: 'miniprogram',
          timestamp: Date.now()
        },
        header: this.getHeaders(headers),
        timeout,
        success: (res) => {
          console.log(`API请求成功 [${method}] ${url}:`, res);
          
          if (res.statusCode === 200) {
            const { data } = res;
            
            // 统一处理响应格式
            if (data.success || data.code === 0) {
              resolve(data);
            } else {
              // 处理业务错误
              const error = new Error(data.message || data.msg || '请求失败');
              error.code = data.code;
              error.data = data;
              reject(error);
            }
          } else if (res.statusCode === 401) {
            // 处理未授权错误
            this.handleUnauthorized();
            reject(new Error('登录已过期，请重新登录'));
          } else {
            reject(new Error(`请求失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          console.error(`API请求失败 [${method}] ${url}:`, error);
          reject(new Error(error.errMsg || '网络请求失败'));
        }
      });
    });
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
    wx.removeStorageSync('loginCode');
    
    // 跳转到登录页面
    wx.navigateTo({
      url: '/pages/user/login/index'
    });
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    return this.request({
      url: params ? `${url}?${this.buildQuery(params)}` : url,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  /**
   * 构建查询字符串
   * @param {Object} params - 查询参数
   * @returns {string} 查询字符串
   */
  buildQuery(params) {
    return Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  }
}

// 创建单例实例
const apiConfig = new ApiConfig();

export default apiConfig;

// 导出常用方法
export const { get, post, put, delete: del, request } = apiConfig;
