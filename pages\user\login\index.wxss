.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 60rpx;
}

.login-header {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 120rpx;
  padding-bottom: 80rpx;
}

.login-logo {
  margin-bottom: 40rpx;
}

.login-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.login-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-content {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.login-btn-wrapper {
  margin-bottom: 40rpx;
}

.phone-login-section {
  margin-top: 20rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.phone-input-wrapper {
  margin-bottom: 30rpx;
}

.phone-input {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  color: #ffffff;
}

.verification-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.code-input {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  color: #ffffff;
}

.send-code-btn {
  width: 200rpx;
  height: 80rpx;
}

.phone-login-btn-wrapper {
  margin-bottom: 40rpx;
}

.switch-login-type {
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 20rpx;
  text-decoration: underline;
}

.login-footer {
  padding: 40rpx 0 60rpx;
}

.agreement-text {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.5;
}

.agreement-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

/* 弹窗样式 */
.agreement-popup {
  width: 600rpx;
  max-height: 800rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.agreement-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.agreement-content {
  flex: 1;
  max-height: 500rpx;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.agreement-actions {
  display: flex;
  justify-content: center;
}

/* TDesign 组件样式覆盖 */
.t-button--theme-primary {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.t-button--theme-primary:not(.t-button--disabled):active {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

.t-button--disabled {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.4) !important;
}

.t-input__control {
  color: #ffffff !important;
}

.t-input__control::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}
