:host {
  background-color: #f5f5f5;
}
page view {
  box-sizing: border-box;
}
.person-info {
  padding-top: 20rpx;
}

.person-info__btn {
  width: 100%;
  border: 2rpx solid #fa550f;
  border-radius: 48rpx;
  padding: 18rpx 0;
  display: flex;
  align-self: center;
  justify-content: center;
  color: #fa550f;
  font-size: 32rpx;
  font-weight: 500;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.person-info__btn:active {
  background-color: #fa550f;
  color: #ffffff;
  transform: scale(0.98);
}
.person-info__wrapper {
  width: 100%;
  padding: 0 32rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  position: absolute;
  bottom: 0;
  left: 0;
}

.avatarUrl {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50% !important;
  overflow: hidden;
}

.t-class-confirm {
  color: #fa550f !important;
}

.person-info .order-group__left {
  margin-right: 0;
}
.person-info .t-cell-class {
  height: 112rpx;
}
