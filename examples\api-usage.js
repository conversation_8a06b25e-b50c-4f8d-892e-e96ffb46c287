/**
 * API使用示例
 * 展示如何在页面中使用登录API
 */

import { wechatLogin, phoneLogin, sendVerificationCode, logout } from '../services/auth/login';
import Toast from 'tdesign-miniprogram/toast/index';

// 示例页面
Page({
  data: {
    phoneNumber: '',
    verificationCode: '',
    loginLoading: false
  },

  /**
   * 示例1: 微信登录
   */
  async handleWechatLogin() {
    try {
      this.setData({ loginLoading: true });

      // 1. 获取微信登录凭证
      const loginRes = await this.wxLogin();
      console.log('微信登录凭证:', loginRes.code);

      // 2. 获取用户信息（如果支持）
      let userInfo = null;
      try {
        const userProfile = await this.getUserProfile();
        userInfo = userProfile.userInfo;
      } catch (error) {
        console.log('获取用户信息失败，使用默认信息');
        userInfo = {
          nickName: '微信用户',
          avatarUrl: 'default-avatar-url',
          gender: 0
        };
      }

      // 3. 调用登录API
      const result = await wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      });

      console.log('登录成功:', result);
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: '登录成功',
        theme: 'success',
      });

      // 4. 跳转到用户中心
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/usercenter/index'
        });
      }, 1500);

    } catch (error) {
      console.error('微信登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败',
        theme: 'error',
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  /**
   * 示例2: 发送验证码
   */
  async handleSendCode() {
    try {
      const { phoneNumber } = this.data;
      
      if (!this.validatePhone(phoneNumber)) {
        Toast({
          context: this,
          selector: '#t-toast',
          message: '请输入正确的手机号',
          theme: 'error',
        });
        return;
      }

      // 调用发送验证码API
      const result = await sendVerificationCode(phoneNumber);
      console.log('验证码发送成功:', result);

      Toast({
        context: this,
        selector: '#t-toast',
        message: '验证码已发送',
        theme: 'success',
      });

      // 开始倒计时
      this.startCountdown();

    } catch (error) {
      console.error('发送验证码失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '发送验证码失败',
        theme: 'error',
      });
    }
  },

  /**
   * 示例3: 手机号登录
   */
  async handlePhoneLogin() {
    try {
      const { phoneNumber, verificationCode } = this.data;

      if (!this.validatePhone(phoneNumber)) {
        Toast({
          context: this,
          selector: '#t-toast',
          message: '请输入正确的手机号',
          theme: 'error',
        });
        return;
      }

      if (!verificationCode || verificationCode.length !== 6) {
        Toast({
          context: this,
          selector: '#t-toast',
          message: '请输入6位验证码',
          theme: 'error',
        });
        return;
      }

      this.setData({ loginLoading: true });

      // 调用手机号登录API
      const result = await phoneLogin({
        phoneNumber,
        code: verificationCode
      });

      console.log('手机号登录成功:', result);

      Toast({
        context: this,
        selector: '#t-toast',
        message: '登录成功',
        theme: 'success',
      });

      // 跳转到用户中心
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/usercenter/index'
        });
      }, 1500);

    } catch (error) {
      console.error('手机号登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败',
        theme: 'error',
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  /**
   * 示例4: 退出登录
   */
  async handleLogout() {
    try {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            // 调用退出登录API
            const result = await logout();
            console.log('退出登录成功:', result);

            Toast({
              context: this,
              selector: '#t-toast',
              message: '已退出登录',
              theme: 'success',
            });

            // 跳转到登录页面
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/user/login/index'
              });
            }, 1500);
          }
        }
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '退出登录失败',
        theme: 'error',
      });
    }
  },

  /**
   * 辅助方法: 微信登录
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 辅助方法: 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      if (wx.getUserProfile) {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('getUserProfile not supported'));
      }
    });
  },

  /**
   * 辅助方法: 验证手机号
   */
  validatePhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  },

  /**
   * 辅助方法: 开始倒计时
   */
  startCountdown() {
    let countdown = 60;
    this.setData({ countdown });

    const timer = setInterval(() => {
      countdown--;
      this.setData({ countdown });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({ countdown: 0 });
      }
    }, 1000);
  },

  /**
   * 输入事件处理
   */
  onPhoneInput(e) {
    this.setData({
      phoneNumber: e.detail.value
    });
  },

  onCodeInput(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  }
});

/**
 * 在其他页面中使用登录状态检查
 */
export function checkLoginStatus() {
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('token');
  
  if (!userInfo || !token) {
    // 未登录，跳转到登录页面
    wx.navigateTo({
      url: '/pages/user/login/index'
    });
    return false;
  }
  
  return true;
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser() {
  return wx.getStorageSync('userInfo') || null;
}

/**
 * 获取当前用户token
 */
export function getCurrentToken() {
  return wx.getStorageSync('token') || null;
}
