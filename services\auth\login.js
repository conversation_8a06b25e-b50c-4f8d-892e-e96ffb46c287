import { config } from '../../config/index';
import apiConfig from '../api/config';

/**
 * 微信登录
 * @param {Object} params - 登录参数
 * @param {string} params.code - 微信登录凭证
 * @param {Object} params.userInfo - 用户信息
 * @returns {Promise} 登录结果
 */
export function wechatLogin(params) {
  if (config.useMock) {
    return mockWechatLogin(params);
  }

  // 使用API配置进行微信登录
  return apiConfig.post('/auth/wechat-login', {
    code: params.code,
    userInfo: params.userInfo
  }).then(data => {
    // 保存登录信息到本地存储
    const userInfo = data.data?.userInfo || data.userInfo;
    const token = data.data?.token || data.token;

    if (userInfo && token) {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('token', token);
      wx.setStorageSync('loginCode', params.code);
    }

    return data;
  });
}

/**
 * 手机号登录
 * @param {Object} params - 登录参数
 * @param {string} params.phoneNumber - 手机号
 * @param {string} params.code - 验证码
 * @returns {Promise} 登录结果
 */
export function phoneLogin(params) {
  if (config.useMock) {
    return mockPhoneLogin(params);
  }

  // 使用API配置进行手机号登录
  return apiConfig.post('/auth/phone-login', {
    phoneNumber: params.phoneNumber,
    code: params.code
  }).then(data => {
    // 保存登录信息到本地存储
    const userInfo = data.data?.userInfo || data.userInfo;
    const token = data.data?.token || data.token;

    if (userInfo && token) {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('token', token);
    }

    return data;
  });
}

/**
 * 发送验证码
 * @param {string} phoneNumber - 手机号
 * @returns {Promise} 发送结果
 */
export function sendVerificationCode(phoneNumber) {
  if (config.useMock) {
    return mockSendVerificationCode(phoneNumber);
  }

  // 使用API配置发送验证码
  return apiConfig.post('/auth/send-code', {
    phoneNumber
  });
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
  if (config.useMock) {
    return mockLogout();
  }

  // 使用API配置退出登录
  return apiConfig.post('/auth/logout')
    .then(data => {
      // 清除本地存储
      clearLocalStorage();
      return data;
    })
    .catch(error => {
      // 即使接口失败，也清除本地存储
      clearLocalStorage();
      return { success: true, message: '已清除本地登录信息' };
    });
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export function checkLoginStatus() {
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('token');
  return !!(userInfo && token);
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export function getCurrentUser() {
  return wx.getStorageSync('userInfo') || null;
}

/**
 * 清除本地存储
 */
function clearLocalStorage() {
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
  wx.removeStorageSync('loginCode');
}

// ========== Mock 函数 ==========

/**
 * 模拟微信登录
 */
function mockWechatLogin(params) {
  const { delay } = require('../_utils/delay');
  
  return delay(1000).then(() => {
    const userInfo = {
      ...params.userInfo,
      id: 'mock_user_' + Date.now(),
    };
    
    const token = 'mock_token_' + Date.now();
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    wx.setStorageSync('loginCode', params.code);
    
    return {
      success: true,
      data: {
        userInfo,
        token
      }
    };
  });
}

/**
 * 模拟手机号登录
 */
function mockPhoneLogin(params) {
  const { delay } = require('../_utils/delay');
  
  return delay(1000).then(() => {
    // 简单验证码校验
    if (params.code !== '123456') {
      throw new Error('验证码错误');
    }
    
    const userInfo = {
      id: 'phone_user_' + Date.now(),
      nickName: '手机用户',
      avatarUrl: 'https://tdesign.gtimg.com/miniprogram/template/retail/usercenter/<EMAIL>',
      phoneNumber: params.phoneNumber,
      gender: 0
    };
    
    const token = 'phone_token_' + Date.now();
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    
    return {
      success: true,
      data: {
        userInfo,
        token
      }
    };
  });
}

/**
 * 模拟发送验证码
 */
function mockSendVerificationCode(phoneNumber) {
  const { delay } = require('../_utils/delay');
  
  return delay(500).then(() => {
    console.log('模拟发送验证码到:', phoneNumber);
    console.log('验证码: 123456');
    
    return {
      success: true,
      message: '验证码已发送'
    };
  });
}

/**
 * 模拟退出登录
 */
function mockLogout() {
  const { delay } = require('../_utils/delay');
  
  return delay(500).then(() => {
    clearLocalStorage();
    
    return {
      success: true,
      message: '退出登录成功'
    };
  });
}
