import { config } from '../../config/index';

/**
 * 微信登录
 * @param {Object} params - 登录参数
 * @param {string} params.code - 微信登录凭证
 * @param {Object} params.userInfo - 用户信息
 * @returns {Promise} 登录结果
 */
export function wechatLogin(params) {
  if (config.useMock) {
    return mockWechatLogin(params);
  }
  
  // 真实的微信登录接口
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.apiUrl}/auth/wechat-login`,
      method: 'POST',
      data: params,
      success: (res) => {
        if (res.data.success) {
          resolve(res.data);
        } else {
          reject(new Error(res.data.message || '登录失败'));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 手机号登录
 * @param {Object} params - 登录参数
 * @param {string} params.phoneNumber - 手机号
 * @param {string} params.code - 验证码
 * @returns {Promise} 登录结果
 */
export function phoneLogin(params) {
  if (config.useMock) {
    return mockPhoneLogin(params);
  }
  
  // 真实的手机号登录接口
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.apiUrl}/auth/phone-login`,
      method: 'POST',
      data: params,
      success: (res) => {
        if (res.data.success) {
          resolve(res.data);
        } else {
          reject(new Error(res.data.message || '登录失败'));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 发送验证码
 * @param {string} phoneNumber - 手机号
 * @returns {Promise} 发送结果
 */
export function sendVerificationCode(phoneNumber) {
  if (config.useMock) {
    return mockSendVerificationCode(phoneNumber);
  }
  
  // 真实的发送验证码接口
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.apiUrl}/auth/send-code`,
      method: 'POST',
      data: { phoneNumber },
      success: (res) => {
        if (res.data.success) {
          resolve(res.data);
        } else {
          reject(new Error(res.data.message || '发送验证码失败'));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
  if (config.useMock) {
    return mockLogout();
  }
  
  // 真实的退出登录接口
  const token = wx.getStorageSync('token');
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.apiUrl}/auth/logout`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        // 无论接口是否成功，都清除本地存储
        clearLocalStorage();
        resolve(res.data);
      },
      fail: (error) => {
        // 即使接口失败，也清除本地存储
        clearLocalStorage();
        reject(error);
      }
    });
  });
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export function checkLoginStatus() {
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('token');
  return !!(userInfo && token);
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export function getCurrentUser() {
  return wx.getStorageSync('userInfo') || null;
}

/**
 * 清除本地存储
 */
function clearLocalStorage() {
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
  wx.removeStorageSync('loginCode');
}

// ========== Mock 函数 ==========

/**
 * 模拟微信登录
 */
function mockWechatLogin(params) {
  const { delay } = require('../_utils/delay');
  
  return delay(1000).then(() => {
    const userInfo = {
      ...params.userInfo,
      id: 'mock_user_' + Date.now(),
    };
    
    const token = 'mock_token_' + Date.now();
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    wx.setStorageSync('loginCode', params.code);
    
    return {
      success: true,
      data: {
        userInfo,
        token
      }
    };
  });
}

/**
 * 模拟手机号登录
 */
function mockPhoneLogin(params) {
  const { delay } = require('../_utils/delay');
  
  return delay(1000).then(() => {
    // 简单验证码校验
    if (params.code !== '123456') {
      throw new Error('验证码错误');
    }
    
    const userInfo = {
      id: 'phone_user_' + Date.now(),
      nickName: '手机用户',
      avatarUrl: 'https://tdesign.gtimg.com/miniprogram/template/retail/usercenter/<EMAIL>',
      phoneNumber: params.phoneNumber,
      gender: 0
    };
    
    const token = 'phone_token_' + Date.now();
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    
    return {
      success: true,
      data: {
        userInfo,
        token
      }
    };
  });
}

/**
 * 模拟发送验证码
 */
function mockSendVerificationCode(phoneNumber) {
  const { delay } = require('../_utils/delay');
  
  return delay(500).then(() => {
    console.log('模拟发送验证码到:', phoneNumber);
    console.log('验证码: 123456');
    
    return {
      success: true,
      message: '验证码已发送'
    };
  });
}

/**
 * 模拟退出登录
 */
function mockLogout() {
  const { delay } = require('../_utils/delay');
  
  return delay(500).then(() => {
    clearLocalStorage();
    
    return {
      success: true,
      message: '退出登录成功'
    };
  });
}
