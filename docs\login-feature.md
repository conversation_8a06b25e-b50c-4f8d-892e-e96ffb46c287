# 切换账号登录功能说明

## 功能概述

本项目已实现完整的切换账号登录功能，包括微信登录和手机号登录两种方式。用户可以在个人信息页面点击"切换账号登录"按钮来退出当前账号并重新登录。

## 功能特性

### 1. 登录方式
- **微信一键登录**: 使用微信授权快速登录
- **手机号登录**: 通过手机号和验证码登录
- **登录方式切换**: 可在两种登录方式之间切换

### 2. 登录状态管理
- 自动检查登录状态
- 本地存储用户信息和token
- 登录状态持久化

### 3. 用户体验
- 美观的登录界面设计
- 加载状态提示
- 错误处理和用户反馈
- 验证码倒计时功能

## 文件结构

```
pages/user/login/           # 登录页面
├── index.js               # 登录逻辑
├── index.wxml             # 登录界面
├── index.wxss             # 登录样式
└── index.json             # 页面配置

services/auth/              # 登录服务
└── login.js               # 登录相关API

pages/user/person-info/     # 个人信息页面
├── index.js               # 包含切换账号功能
├── index.wxml             # 包含切换账号按钮
└── index.wxss             # 按钮样式

pages/usercenter/           # 用户中心
└── index.js               # 登录状态检查
```

## 使用方法

### 1. 切换账号登录
1. 进入个人信息页面 (`pages/user/person-info/index`)
2. 点击页面底部的"切换账号登录"按钮
3. 确认退出当前账号
4. 自动跳转到登录页面

### 2. 微信登录
1. 在登录页面点击"微信一键登录"按钮
2. 授权获取微信用户信息
3. 登录成功后跳转到用户中心

### 3. 手机号登录
1. 在登录页面点击"使用手机号登录"
2. 输入手机号码
3. 点击"发送验证码"按钮
4. 输入验证码（测试环境默认为: 123456）
5. 点击"手机号登录"按钮

## 技术实现

### 1. 登录服务 (services/auth/login.js)
- `wechatLogin()`: 微信登录
- `phoneLogin()`: 手机号登录
- `sendVerificationCode()`: 发送验证码
- `logout()`: 退出登录
- `checkLoginStatus()`: 检查登录状态

### 2. 状态管理
- 使用 `wx.setStorageSync()` 存储用户信息
- 使用 `wx.getStorageSync()` 获取用户信息
- 使用 `wx.removeStorageSync()` 清除用户信息

### 3. 页面跳转
- 登录成功后跳转到用户中心
- 退出登录后跳转到登录页面
- 未登录时自动跳转到登录页面

## 测试说明

### 1. 微信登录测试
- 在微信开发者工具中测试微信登录功能
- 需要配置正确的AppID和AppSecret

### 2. 手机号登录测试
- 测试环境验证码固定为: `123456`
- 可以使用任意11位手机号进行测试

### 3. 切换账号测试
1. 先完成一次登录
2. 进入个人信息页面
3. 点击"切换账号登录"按钮
4. 确认退出并重新登录

## 配置说明

### 1. 页面路由配置 (app.json)
```json
{
  "subpackages": [
    {
      "root": "pages/user",
      "name": "user",
      "pages": [
        "person-info/index",
        "address/list/index", 
        "address/edit/index",
        "name-edit/index",
        "login/index"  // 新增登录页面
      ]
    }
  ]
}
```

### 2. 登录服务配置
- 支持Mock模式和真实API模式
- 通过 `config.useMock` 控制模式切换

## 注意事项

1. **权限配置**: 确保在 `app.json` 中配置了必要的权限
2. **网络请求**: 真实环境需要配置正确的API地址
3. **微信授权**: 需要在微信公众平台配置域名白名单
4. **用户体验**: 建议添加加载状态和错误处理

## 扩展功能

可以基于现有功能扩展以下特性：
- 第三方登录（QQ、微博等）
- 生物识别登录（指纹、面容）
- 多账号管理
- 登录历史记录
- 安全设置（登录密码、手势密码等）
