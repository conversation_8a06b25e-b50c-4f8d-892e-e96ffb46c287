# API接口对接文档

## 概述

本文档说明如何将微信小程序的登录功能与您的后端API接口对接。

## 配置说明

### 1. 环境配置

在 `config/index.js` 中配置您的API地址：

```javascript
export const config = {
  /** 是否使用mock代替api返回 */
  useMock: false, // 设置为 false 启用真实API

  /** API基础地址 */
  apiUrl: 'https://your-api-domain.com/api',

  /** 开发环境API地址 */
  devApiUrl: 'http://localhost:3000/api',

  /** 生产环境API地址 */
  prodApiUrl: 'https://your-production-api.com/api',
};
```

### 2. 自动环境切换

系统会根据小程序的运行环境自动选择API地址：
- **开发版**: 使用 `devApiUrl`
- **体验版**: 使用 `apiUrl`
- **正式版**: 使用 `prodApiUrl`

## API接口规范

### 1. 微信登录接口

**接口地址**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "微信登录凭证",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像地址",
    "gender": 1
  },
  "platform": "miniprogram",
  "timestamp": 1640995200000
}
```

**响应格式**:
```json
{
  "success": true,
  "code": 0,
  "message": "登录成功",
  "data": {
    "userInfo": {
      "id": "用户ID",
      "nickName": "用户昵称",
      "avatarUrl": "头像地址",
      "phoneNumber": "手机号"
    },
    "token": "JWT令牌"
  }
}
```

### 2. 手机号登录接口

**接口地址**: `POST /auth/phone-login`

**请求参数**:
```json
{
  "phoneNumber": "13800138000",
  "code": "123456",
  "platform": "miniprogram",
  "timestamp": 1640995200000
}
```

**响应格式**:
```json
{
  "success": true,
  "code": 0,
  "message": "登录成功",
  "data": {
    "userInfo": {
      "id": "用户ID",
      "nickName": "手机用户",
      "phoneNumber": "13800138000"
    },
    "token": "JWT令牌"
  }
}
```

### 3. 发送验证码接口

**接口地址**: `POST /auth/send-code`

**请求参数**:
```json
{
  "phoneNumber": "13800138000",
  "platform": "miniprogram",
  "timestamp": 1640995200000
}
```

**响应格式**:
```json
{
  "success": true,
  "code": 0,
  "message": "验证码发送成功"
}
```

### 4. 退出登录接口

**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer JWT令牌
```

**请求参数**:
```json
{
  "platform": "miniprogram",
  "timestamp": 1640995200000
}
```

**响应格式**:
```json
{
  "success": true,
  "code": 0,
  "message": "退出登录成功"
}
```

## 错误处理

### 1. HTTP状态码

- `200`: 请求成功
- `401`: 未授权（token过期或无效）
- `400`: 请求参数错误
- `500`: 服务器内部错误

### 2. 业务错误码

```json
{
  "success": false,
  "code": 1001,
  "message": "验证码错误",
  "data": null
}
```

常见错误码：
- `1001`: 验证码错误
- `1002`: 验证码已过期
- `1003`: 手机号格式错误
- `1004`: 用户不存在
- `1005`: 微信登录失败

## 后端实现示例

### Node.js + Express 示例

```javascript
// 微信登录接口
app.post('/api/auth/wechat-login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    // 1. 使用code向微信服务器获取openid和session_key
    const wxResult = await getWxUserInfo(code);
    
    // 2. 查找或创建用户
    let user = await User.findOne({ openid: wxResult.openid });
    if (!user) {
      user = await User.create({
        openid: wxResult.openid,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender
      });
    }
    
    // 3. 生成JWT token
    const token = jwt.sign({ userId: user.id }, JWT_SECRET);
    
    res.json({
      success: true,
      code: 0,
      message: '登录成功',
      data: {
        userInfo: {
          id: user.id,
          nickName: user.nickName,
          avatarUrl: user.avatarUrl,
          phoneNumber: user.phoneNumber
        },
        token
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      code: 500,
      message: error.message
    });
  }
});

// 手机号登录接口
app.post('/api/auth/phone-login', async (req, res) => {
  try {
    const { phoneNumber, code } = req.body;
    
    // 1. 验证验证码
    const isValidCode = await verifyCode(phoneNumber, code);
    if (!isValidCode) {
      return res.json({
        success: false,
        code: 1001,
        message: '验证码错误'
      });
    }
    
    // 2. 查找或创建用户
    let user = await User.findOne({ phoneNumber });
    if (!user) {
      user = await User.create({
        phoneNumber,
        nickName: '手机用户'
      });
    }
    
    // 3. 生成JWT token
    const token = jwt.sign({ userId: user.id }, JWT_SECRET);
    
    res.json({
      success: true,
      code: 0,
      message: '登录成功',
      data: {
        userInfo: {
          id: user.id,
          nickName: user.nickName,
          phoneNumber: user.phoneNumber
        },
        token
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      code: 500,
      message: error.message
    });
  }
});
```

## 测试步骤

### 1. 启用真实API

在 `config/index.js` 中设置：
```javascript
useMock: false
```

### 2. 配置API地址

根据您的后端服务地址配置 `apiUrl`

### 3. 测试登录流程

1. 在小程序中点击"微信一键登录"
2. 查看控制台输出的API请求和响应
3. 确认用户信息和token正确保存

### 4. 调试工具

- 使用微信开发者工具的网络面板查看请求
- 在后端添加日志输出请求参数
- 使用Postman等工具测试API接口

## 注意事项

1. **域名配置**: 在微信公众平台配置request合法域名
2. **HTTPS要求**: 生产环境必须使用HTTPS
3. **Token管理**: 实现token刷新机制
4. **错误处理**: 完善的错误处理和用户提示
5. **安全考虑**: 验证码有效期、防刷机制等

## 常见问题

### Q: 请求失败，提示"不在以下 request 合法域名列表中"
A: 需要在微信公众平台配置request合法域名

### Q: 微信登录获取不到用户信息
A: 检查getUserProfile的调用时机和授权流程

### Q: token过期如何处理
A: 实现token自动刷新或引导用户重新登录

### Q: 如何调试API接口
A: 使用微信开发者工具的网络面板和后端日志
