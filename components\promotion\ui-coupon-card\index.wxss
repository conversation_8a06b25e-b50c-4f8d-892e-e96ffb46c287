.wr-coupon {
  display: flex;
  background-image: url('https://tdesign.gtimg.com/miniprogram/template/retail/coupon/coupon-bg-nocorners.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  margin-bottom: 24rpx;
  overflow: hidden;
}
.theme-weak.wr-coupon {
  background-image: url('https://tdesign.gtimg.com/miniprogram/template/retail/coupon/coupon-bg-grey2.png');
}

.wr-coupon__left {
  width: 200rpx;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  color: #fa4126;
  overflow: hidden;
  position: relative;
}
.theme-weak .wr-coupon__left {
  color: #333;
}

.wr-coupon__left--value {
  font-size: 64rpx;
  line-height: 88rpx;
  font-weight: bold;
  font-family: 'DIN Alternate', cursive;
}
.wr-coupon__left--value-int {
  font-size: 48rpx;
  line-height: 88rpx;
}
.wr-coupon__left--value-decimal {
  font-size: 36rpx;
  line-height: 48rpx;
}
.wr-coupon__left--image {
  width: 128rpx;
  height: 128rpx;
  border-radius: 8px;
  margin-top: 30rpx;
}
.wr-coupon__left--unit {
  font-size: 24rpx;
  line-height: 32rpx;
}
.wr-coupon__left--desc {
  font-size: 24rpx;
  line-height: 32rpx;
  color: #fa4126;
}

.theme-weak .wr-coupon__left--desc {
  color: #333;
}

.wr-coupon__right {
  flex-grow: 1;
  padding: 0 20rpx;
  height: 180rpx;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.wr-coupon__right--title {
  display: flex;
  -webkit-display: flex;
  flex-direction: column;
  align-items: flex-start;
  color: #999999;
  font-size: 24rpx;
  flex: 1;
}
.wr-coupon__right--title .coupon-title {
  max-width: 320rpx;
  color: #333333;
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: bold;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
}
.wr-coupon__right--title .coupon-time {
  margin-top: 16rpx;
  /* //   letter-spacing: -0.05em; */
}
.wr-coupon__right--title .coupon-desc {
  margin-top: 8rpx;
}
.wr-coupon__right--title .coupon-arrow {
  font-size: 22rpx;
}
.wr-coupon__right--oper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.wr-coupon__mask {
  width: 702rpx;
  height: 182rpx;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #ffffff;
  opacity: 0.5;
}
.wr-coupon__tag {
  position: absolute;
  top: 8px;
  right: -24rpx;
  text-align: center;
  width: 106rpx;
  height: 28rpx;
  opacity: 0.9;
  font-size: 20rpx;
  line-height: 28rpx;
  color: #fa4126;
  border: 0.5px solid #fa4126;
  box-sizing: border-box;
  transform: rotate(45deg);
}
.wr-coupon__seal {
  width: 128rpx;
  height: 128rpx;
  position: absolute;
  top: 0;
  right: 0;
  background-size: 100% 100%;
}

.wr-coupon__seal.seal-useless {
  background-image: url('https://tdesign.gtimg.com/miniprogram/template/retail/coupon/seal-used.png');
}

.wr-coupon__seal.seal-disabled {
  background-image: url('https://tdesign.gtimg.com/miniprogram/template/retail/coupon/coupon-expired.png');
}
