import { phoneRegCheck } from '../../../utils/util';
import { wechatLogin, phoneLogin, sendVerificationCode } from '../../../services/auth/login';
import Toast from 'tdesign-miniprogram/toast/index';

Page({
  data: {
    loginLoading: false,
    phoneLoginLoading: false,
    showPhoneLogin: false,
    phoneNumber: '',
    verificationCode: '',
    codeCountdown: 0,
    canSendCode: false,
    canPhoneLogin: false,
    showAgreement: false,
    showPrivacy: false,
  },

  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      // 已登录，跳转到个人中心
      wx.switchTab({
        url: '/pages/usercenter/index'
      });
    }
  },

  // 微信登录
  async handleWechatLogin() {
    if (this.data.loginLoading) return;
    
    this.setData({ loginLoading: true });
    
    try {
      // 获取微信登录凭证
      const loginRes = await this.wxLogin();
      console.log('微信登录凭证:', loginRes.code);
      
      // 获取用户信息授权
      const userProfile = await this.getUserProfile();
      console.log('用户信息:', userProfile);
      
      // 调用登录服务
      await wechatLogin({
        code: loginRes.code,
        userInfo: userProfile.userInfo
      });
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: '登录成功',
        theme: 'success',
      });
      
      // 跳转到个人中心
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/usercenter/index'
        });
      }, 1500);
      
    } catch (error) {
      console.error('微信登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败，请重试',
        theme: 'error',
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      });
    });
  },



  // 手机号输入
  onPhoneChange(e) {
    const phoneNumber = e.detail.value;
    this.setData({ 
      phoneNumber,
      canSendCode: phoneRegCheck(phoneNumber)
    });
    this.checkPhoneLoginStatus();
  },

  // 验证码输入
  onCodeChange(e) {
    const verificationCode = e.detail.value;
    this.setData({ verificationCode });
    this.checkPhoneLoginStatus();
  },

  // 检查手机号登录状态
  checkPhoneLoginStatus() {
    const { phoneNumber, verificationCode } = this.data;
    const canPhoneLogin = phoneRegCheck(phoneNumber) && verificationCode.length === 6;
    this.setData({ canPhoneLogin });
  },

  // 发送验证码
  async sendVerificationCode() {
    if (!this.data.canSendCode || this.data.codeCountdown > 0) return;
    
    try {
      // 调用发送验证码服务
      await sendVerificationCode(this.data.phoneNumber);
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: '验证码已发送',
        theme: 'success',
      });
      
      // 开始倒计时
      this.startCountdown();
      
    } catch (error) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '发送验证码失败，请重试',
        theme: 'error',
      });
    }
  },



  // 开始倒计时
  startCountdown() {
    let countdown = 60;
    this.setData({ codeCountdown: countdown });
    
    const timer = setInterval(() => {
      countdown--;
      this.setData({ codeCountdown: countdown });
      
      if (countdown <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  },

  // 手机号登录
  async handlePhoneLogin() {
    if (!this.data.canPhoneLogin || this.data.phoneLoginLoading) return;
    
    this.setData({ phoneLoginLoading: true });
    
    try {
      // 调用手机号登录服务
      await phoneLogin({
        phoneNumber: this.data.phoneNumber,
        code: this.data.verificationCode
      });
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: '登录成功',
        theme: 'success',
      });
      
      // 跳转到个人中心
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/usercenter/index'
        });
      }, 1500);
      
    } catch (error) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败，请重试',
        theme: 'error',
      });
    } finally {
      this.setData({ phoneLoginLoading: false });
    }
  },



  // 切换登录方式
  toggleLoginType() {
    this.setData({ 
      showPhoneLogin: !this.data.showPhoneLogin,
      phoneNumber: '',
      verificationCode: '',
      codeCountdown: 0,
      canSendCode: false,
      canPhoneLogin: false
    });
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({ showAgreement: true });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({ showPrivacy: true });
  },

  // 关闭协议弹窗
  closeAgreement() {
    this.setData({ showAgreement: false });
  },

  onAgreementClose(e) {
    this.setData({ showAgreement: e.detail.visible });
  },

  // 关闭隐私政策弹窗
  closePrivacy() {
    this.setData({ showPrivacy: false });
  },

  onPrivacyClose(e) {
    this.setData({ showPrivacy: e.detail.visible });
  },
});
